package ai

import (
	"fmt"
)

/**
 * 游戏场景专用JSON Schema定义
 * @description 为不同游戏场景定义具体的JSON Schema结构
 */

// GameSchemaRegistry 游戏Schema注册表
type GameSchemaRegistry struct {
	schemas map[string]*JSONSchema
}

// NewGameSchemaRegistry 创建游戏Schema注册表
func NewGameSchemaRegistry() *GameSchemaRegistry {
	registry := &GameSchemaRegistry{
		schemas: make(map[string]*JSONSchema),
	}
	
	// 注册所有游戏场景的Schema
	registry.registerAllSchemas()
	
	return registry
}

// GetSchema 获取指定类型的Schema
func (gsr *GameSchemaRegistry) GetSchema(contentType string) (*JSONSchema, error) {
	schema, exists := gsr.schemas[contentType]
	if !exists {
		return nil, fmt.Errorf("未找到内容类型 '%s' 的Schema定义", contentType)
	}
	return schema, nil
}

// GetSchemaAsMap 获取指定类型的Schema并转换为map格式
func (gsr *GameSchemaRegistry) GetSchemaAsMap(contentType string) (map[string]interface{}, error) {
	schema, err := gsr.GetSchema(contentType)
	if err != nil {
		return nil, err
	}
	return schema.ToMap(), nil
}

// ListAvailableTypes 列出所有可用的内容类型
func (gsr *GameSchemaRegistry) ListAvailableTypes() []string {
	types := make([]string, 0, len(gsr.schemas))
	for contentType := range gsr.schemas {
		types = append(types, contentType)
	}
	return types
}

// registerAllSchemas 注册所有游戏场景的Schema
func (gsr *GameSchemaRegistry) registerAllSchemas() {
	// 场景生成Schema
	gsr.schemas["scene"] = gsr.createSceneSchema()
	
	// 角色生成Schema
	gsr.schemas["character"] = gsr.createCharacterSchema()
	
	// 事件生成Schema
	gsr.schemas["event"] = gsr.createEventSchema()
	
	// 对话生成Schema
	gsr.schemas["dialogue"] = gsr.createDialogueSchema()
	
	// 物品生成Schema
	gsr.schemas["item"] = gsr.createItemSchema()
	
	// 世界描述Schema
	gsr.schemas["world_description"] = gsr.createWorldDescriptionSchema()
	
	// 任务生成Schema
	gsr.schemas["quest"] = gsr.createQuestSchema()
	
	// 环境效果Schema
	gsr.schemas["environment_effect"] = gsr.createEnvironmentEffectSchema()
}

// createSceneSchema 创建场景生成Schema
func (gsr *GameSchemaRegistry) createSceneSchema() *JSONSchema {
	// 连接信息Schema
	connectionSchema := NewSchemaBuilder().Object().
		Description("场景连接信息").
		Property("direction", StringSchema("方向（如：北、南、东、西、上、下）")).
		Property("description", StringSchema("连接的详细描述")).
		Property("scene_name", StringSchema("连接到的场景名称")).
		Property("is_locked", NewSchemaBuilder().Boolean().Description("是否被锁定").Build()).
		Property("unlock_condition", StringSchema("解锁条件描述（可选）")).
		Required("direction", "description", "scene_name").
		Build()
	
	return NewSchemaBuilder().Object().
		Description("游戏场景生成结果").
		Property("name", NewSchemaBuilder().String().
			Description("场景名称，应该简洁且富有想象力").
			MinLength(2).MaxLength(50).Build()).
		Property("description", NewSchemaBuilder().String().
			Description("详细的场景描述，包含环境、氛围、关键特征等，应该生动形象").
			MinLength(50).MaxLength(500).Build()).
		Property("atmosphere", EnumSchema("场景氛围类型", 
			"神秘", "温馨", "紧张", "恐怖", "宁静", "热闹", "庄严", "荒凉", "梦幻", "危险")).
		Property("scene_type", EnumSchema("场景类型",
			"室内", "室外", "地下", "城镇", "荒野", "森林", "山脉", "海洋", "天空", "特殊")).
		Property("key_features", StringArraySchema("场景的关键特征列表，每个特征应该具体且有用")).
		Property("possible_actions", StringArraySchema("在此场景中玩家可能进行的行动列表")).
		Property("connections", ArraySchema("场景连接信息列表", connectionSchema)).
		Property("hidden_elements", StringArraySchema("隐藏元素列表，需要特定条件才能发现")).
		Property("danger_level", IntegerSchema("危险等级（1-10）", floatPtr(1), floatPtr(10))).
		Property("lighting", EnumSchema("光照条件", "明亮", "昏暗", "黑暗", "闪烁", "彩色", "自然")).
		Property("sounds", StringArraySchema("环境音效描述列表")).
		Property("smells", StringArraySchema("环境气味描述列表")).
		Required("name", "description", "atmosphere", "scene_type", "key_features", 
			"possible_actions", "connections", "danger_level", "lighting").
		Build()
}

// createCharacterSchema 创建角色生成Schema
func (gsr *GameSchemaRegistry) createCharacterSchema() *JSONSchema {
	// 属性Schema
	attributesSchema := NewSchemaBuilder().Object().
		Description("角色属性").
		Property("strength", IntegerSchema("力量值（1-100）", floatPtr(1), floatPtr(100))).
		Property("intelligence", IntegerSchema("智力值（1-100）", floatPtr(1), floatPtr(100))).
		Property("agility", IntegerSchema("敏捷值（1-100）", floatPtr(1), floatPtr(100))).
		Property("charisma", IntegerSchema("魅力值（1-100）", floatPtr(1), floatPtr(100))).
		Property("health", IntegerSchema("生命值（1-1000）", floatPtr(1), floatPtr(1000))).
		Property("mana", IntegerSchema("魔法值（0-1000）", floatPtr(0), floatPtr(1000))).
		Required("strength", "intelligence", "agility", "charisma", "health").
		Build()
	
	// 外观Schema
	appearanceSchema := NewSchemaBuilder().Object().
		Description("角色外观描述").
		Property("height", StringSchema("身高描述")).
		Property("build", EnumSchema("体型", "瘦弱", "苗条", "标准", "健壮", "肥胖", "魁梧")).
		Property("hair_color", StringSchema("头发颜色")).
		Property("eye_color", StringSchema("眼睛颜色")).
		Property("skin_tone", StringSchema("肤色")).
		Property("distinctive_features", StringArraySchema("显著特征列表")).
		Property("clothing_style", StringSchema("服装风格描述")).
		Required("height", "build", "hair_color", "eye_color", "skin_tone").
		Build()
	
	return NewSchemaBuilder().Object().
		Description("游戏角色生成结果").
		Property("name", NewSchemaBuilder().String().
			Description("角色名称，应该符合游戏世界观").
			MinLength(2).MaxLength(30).Build()).
		Property("description", NewSchemaBuilder().String().
			Description("角色的基本描述，包含外观和给人的第一印象").
			MinLength(50).MaxLength(300).Build()).
		Property("character_type", EnumSchema("角色类型", "玩家", "NPC", "敌人", "商人", "导师", "守卫", "平民")).
		Property("personality", StringArraySchema("性格特征列表，每个特征应该具体且影响行为")).
		Property("background", NewSchemaBuilder().String().
			Description("角色背景故事，解释其来历和经历").
			MinLength(100).MaxLength(500).Build()).
		Property("skills", StringArraySchema("技能列表，包含战斗和非战斗技能")).
		Property("dialogue_style", StringSchema("对话风格描述，包含语言习惯和表达方式")).
		Property("motivations", StringArraySchema("动机列表，驱动角色行为的内在原因")).
		Property("fears", StringArraySchema("恐惧列表，角色害怕或回避的事物")).
		Property("goals", StringArraySchema("目标列表，角色想要达成的目的")).
		Property("relationships", StringArraySchema("重要关系描述列表")).
		Property("attributes", attributesSchema).
		Property("appearance", appearanceSchema).
		Property("age_range", EnumSchema("年龄段", "儿童", "青少年", "青年", "中年", "老年", "古老")).
		Property("occupation", StringSchema("职业或身份")).
		Property("alignment", EnumSchema("阵营倾向", "善良", "中立", "邪恶", "守序", "混乱")).
		Required("name", "description", "character_type", "personality", "background", 
			"skills", "dialogue_style", "motivations", "attributes", "appearance", 
			"age_range", "occupation", "alignment").
		Build()
}

// createEventSchema 创建事件生成Schema
func (gsr *GameSchemaRegistry) createEventSchema() *JSONSchema {
	// 事件效果Schema
	effectsSchema := NewSchemaBuilder().Object().
		Description("事件效果").
		Property("description", StringSchema("事件效果的详细描述")).
		Property("consequences", StringArraySchema("事件后果列表")).
		Property("rewards", StringArraySchema("可能的奖励列表")).
		Property("penalties", StringArraySchema("可能的惩罚列表")).
		Property("stat_changes", NewSchemaBuilder().Object().
			Description("属性变化").
			Property("health", IntegerSchema("生命值变化", floatPtr(-100), floatPtr(100))).
			Property("mana", IntegerSchema("魔法值变化", floatPtr(-100), floatPtr(100))).
			Property("experience", IntegerSchema("经验值变化", floatPtr(-50), floatPtr(200))).
			Build()).
		Required("description", "consequences").
		Build()
	
	return NewSchemaBuilder().Object().
		Description("游戏事件生成结果").
		Property("name", NewSchemaBuilder().String().
			Description("事件名称，应该简洁且吸引人").
			MinLength(5).MaxLength(50).Build()).
		Property("description", NewSchemaBuilder().String().
			Description("事件的详细描述，包含发生的情况和背景").
			MinLength(100).MaxLength(800).Build()).
		Property("event_type", EnumSchema("事件类型", 
			"随机遭遇", "剧情事件", "角色互动", "环境变化", "战斗", "探索", "谜题", "交易", "任务")).
		Property("priority", IntegerSchema("事件优先级（1-10，数字越大优先级越高）", floatPtr(1), floatPtr(10))).
		Property("duration", IntegerSchema("事件持续时间（分钟）", floatPtr(1), floatPtr(1440))).
		Property("trigger_conditions", StringArraySchema("触发条件列表")).
		Property("participants", StringArraySchema("参与者列表（角色名称或类型）")).
		Property("location_requirements", StringArraySchema("地点要求列表")).
		Property("effects", effectsSchema).
		Property("choices", ArraySchema("玩家选择列表", NewSchemaBuilder().Object().
			Description("玩家选择选项").
			Property("text", StringSchema("选择文本")).
			Property("consequence", StringSchema("选择后果")).
			Required("text", "consequence").
			Build())).
		Property("difficulty", EnumSchema("难度等级", "简单", "普通", "困难", "极难")).
		Property("repeatable", NewSchemaBuilder().Boolean().Description("是否可重复触发").Build()).
		Required("name", "description", "event_type", "priority", "duration", 
			"trigger_conditions", "effects", "difficulty", "repeatable").
		Build()
}

// createDialogueSchema 创建对话生成Schema
func (gsr *GameSchemaRegistry) createDialogueSchema() *JSONSchema {
	return NewSchemaBuilder().Object().
		Description("游戏对话生成结果").
		Property("content", NewSchemaBuilder().String().
			Description("对话内容，应该符合角色性格和当前情境").
			MinLength(10).MaxLength(500).Build()).
		Property("emotion", EnumSchema("情感状态",
			"高兴", "愤怒", "悲伤", "恐惧", "惊讶", "厌恶", "中性", "兴奋", "困惑", "失望")).
		Property("tone", EnumSchema("语调",
			"友好", "敌对", "正式", "随意", "神秘", "威胁", "恳求", "自信", "怯懦", "傲慢")).
		Property("intent", EnumSchema("对话意图",
			"信息传递", "请求帮助", "威胁警告", "友好交流", "商业交易", "任务发布", "情感表达", "指导教学")).
		Property("context_awareness", StringSchema("对当前情境的认知和反应")).
		Property("follow_up_suggestions", StringArraySchema("后续对话建议列表")).
		Property("body_language", StringSchema("肢体语言描述")).
		Property("voice_characteristics", StringSchema("声音特征描述")).
		Required("content", "emotion", "tone", "intent", "context_awareness").
		Build()
}

// createItemSchema 创建物品生成Schema
func (gsr *GameSchemaRegistry) createItemSchema() *JSONSchema {
	// 物品属性Schema
	itemPropertiesSchema := NewSchemaBuilder().Object().
		Description("物品属性").
		Property("durability", IntegerSchema("耐久度（1-100）", floatPtr(1), floatPtr(100))).
		Property("weight", NewSchemaBuilder().Number().
			Description("重量（千克）").Minimum(0.01).Maximum(1000).Build()).
		Property("value", IntegerSchema("价值（金币）", floatPtr(0), floatPtr(100000))).
		Property("rarity", EnumSchema("稀有度", "普通", "罕见", "稀有", "史诗", "传说")).
		Property("magical", NewSchemaBuilder().Boolean().Description("是否为魔法物品").Build()).
		Build()

	return NewSchemaBuilder().Object().
		Description("游戏物品生成结果").
		Property("name", NewSchemaBuilder().String().
			Description("物品名称，应该简洁且富有特色").
			MinLength(2).MaxLength(50).Build()).
		Property("description", NewSchemaBuilder().String().
			Description("物品的详细描述，包含外观、材质、特殊之处").
			MinLength(50).MaxLength(400).Build()).
		Property("item_type", EnumSchema("物品类型",
			"武器", "防具", "工具", "消耗品", "材料", "宝石", "书籍", "钥匙", "装饰品", "特殊物品")).
		Property("sub_type", StringSchema("物品子类型（如：长剑、皮甲、治疗药水等）")).
		Property("properties", itemPropertiesSchema).
		Property("effects", StringArraySchema("物品效果列表")).
		Property("usage_requirements", StringArraySchema("使用要求列表")).
		Property("crafting_materials", StringArraySchema("制作材料列表（如果可制作）")).
		Property("lore", StringSchema("物品背景故事或传说")).
		Property("appearance", StringSchema("外观详细描述")).
		Property("size", EnumSchema("尺寸", "微小", "小型", "中型", "大型", "巨大")).
		Required("name", "description", "item_type", "sub_type", "properties", "size").
		Build()
}

// createWorldDescriptionSchema 创建世界描述Schema
func (gsr *GameSchemaRegistry) createWorldDescriptionSchema() *JSONSchema {
	// 世界规则Schema
	worldRulesSchema := NewSchemaBuilder().Object().
		Description("世界规则").
		Property("magic_system", StringSchema("魔法体系描述")).
		Property("technology_level", EnumSchema("科技水平", "原始", "古代", "中世纪", "文艺复兴", "工业", "现代", "未来")).
		Property("social_structure", StringSchema("社会结构描述")).
		Property("economy", StringSchema("经济体系描述")).
		Property("laws", StringArraySchema("重要法律或规则列表")).
		Build()

	return NewSchemaBuilder().Object().
		Description("游戏世界描述生成结果").
		Property("name", NewSchemaBuilder().String().
			Description("世界名称，应该独特且富有想象力").
			MinLength(3).MaxLength(50).Build()).
		Property("description", NewSchemaBuilder().String().
			Description("世界的总体描述，包含地理、文化、历史概况").
			MinLength(200).MaxLength(1000).Build()).
		Property("theme", EnumSchema("世界主题",
			"奇幻", "科幻", "现代", "历史", "蒸汽朋克", "赛博朋克", "后末日", "超自然", "武侠", "仙侠")).
		Property("geography", StringArraySchema("主要地理特征列表")).
		Property("climate", StringSchema("气候特征描述")).
		Property("major_locations", StringArraySchema("主要地点列表")).
		Property("cultures", StringArraySchema("主要文化或种族列表")).
		Property("history", StringSchema("重要历史事件概述")).
		Property("current_conflicts", StringArraySchema("当前冲突或问题列表")).
		Property("notable_figures", StringArraySchema("重要人物列表")).
		Property("world_rules", worldRulesSchema).
		Property("dangers", StringArraySchema("世界中的主要危险列表")).
		Property("opportunities", StringArraySchema("冒险机会列表")).
		Required("name", "description", "theme", "geography", "climate",
			"major_locations", "cultures", "world_rules").
		Build()
}

// createQuestSchema 创建任务生成Schema
func (gsr *GameSchemaRegistry) createQuestSchema() *JSONSchema {
	// 任务奖励Schema
	questRewardsSchema := NewSchemaBuilder().Object().
		Description("任务奖励").
		Property("experience", IntegerSchema("经验值奖励", floatPtr(0), floatPtr(10000))).
		Property("gold", IntegerSchema("金币奖励", floatPtr(0), floatPtr(50000))).
		Property("items", StringArraySchema("物品奖励列表")).
		Property("reputation", StringSchema("声望变化描述")).
		Property("special_rewards", StringArraySchema("特殊奖励列表")).
		Build()

	// 任务步骤Schema
	questStepSchema := NewSchemaBuilder().Object().
		Description("任务步骤").
		Property("step_number", IntegerSchema("步骤编号", floatPtr(1), floatPtr(20))).
		Property("description", StringSchema("步骤描述")).
		Property("objective", StringSchema("具体目标")).
		Property("location", StringSchema("执行地点")).
		Property("hints", StringArraySchema("提示列表")).
		Required("step_number", "description", "objective").
		Build()

	return NewSchemaBuilder().Object().
		Description("游戏任务生成结果").
		Property("name", NewSchemaBuilder().String().
			Description("任务名称，应该吸引人且概括任务内容").
			MinLength(5).MaxLength(60).Build()).
		Property("description", NewSchemaBuilder().String().
			Description("任务的详细描述，包含背景和目标").
			MinLength(100).MaxLength(600).Build()).
		Property("quest_type", EnumSchema("任务类型",
			"主线任务", "支线任务", "日常任务", "紧急任务", "隐藏任务", "连锁任务", "重复任务")).
		Property("difficulty", EnumSchema("难度等级", "新手", "简单", "普通", "困难", "专家", "传说")).
		Property("estimated_duration", IntegerSchema("预计完成时间（分钟）", floatPtr(5), floatPtr(480))).
		Property("prerequisites", StringArraySchema("前置条件列表")).
		Property("steps", ArraySchema("任务步骤列表", questStepSchema)).
		Property("rewards", questRewardsSchema).
		Property("failure_consequences", StringArraySchema("失败后果列表")).
		Property("time_limit", IntegerSchema("时间限制（小时，0表示无限制）", floatPtr(0), floatPtr(168))).
		Property("repeatable", NewSchemaBuilder().Boolean().Description("是否可重复完成").Build()).
		Property("quest_giver", StringSchema("任务发布者")).
		Property("location", StringSchema("任务主要执行地点")).
		Required("name", "description", "quest_type", "difficulty",
			"estimated_duration", "steps", "rewards", "quest_giver").
		Build()
}

// createEnvironmentEffectSchema 创建环境效果Schema
func (gsr *GameSchemaRegistry) createEnvironmentEffectSchema() *JSONSchema {
	return NewSchemaBuilder().Object().
		Description("环境效果生成结果").
		Property("name", NewSchemaBuilder().String().
			Description("环境效果名称").
			MinLength(3).MaxLength(40).Build()).
		Property("description", NewSchemaBuilder().String().
			Description("环境效果的详细描述").
			MinLength(50).MaxLength(300).Build()).
		Property("effect_type", EnumSchema("效果类型",
			"天气变化", "魔法效应", "自然现象", "超自然事件", "环境危险", "祝福效果", "诅咒效果")).
		Property("duration", IntegerSchema("持续时间（分钟，0表示永久）", floatPtr(0), floatPtr(1440))).
		Property("intensity", EnumSchema("强度等级", "微弱", "轻微", "中等", "强烈", "极强")).
		Property("affected_area", EnumSchema("影响范围", "单点", "小范围", "中等范围", "大范围", "全区域")).
		Property("visual_effects", StringArraySchema("视觉效果描述列表")).
		Property("audio_effects", StringArraySchema("音效描述列表")).
		Property("gameplay_effects", StringArraySchema("游戏机制影响列表")).
		Property("stat_modifiers", NewSchemaBuilder().Object().
			Description("属性修正").
			Property("movement_speed", NewSchemaBuilder().Number().
				Description("移动速度修正（倍数）").Minimum(0.1).Maximum(3.0).Build()).
			Property("visibility", NewSchemaBuilder().Number().
				Description("可见度修正（倍数）").Minimum(0.1).Maximum(2.0).Build()).
			Property("damage_modifier", NewSchemaBuilder().Number().
				Description("伤害修正（倍数）").Minimum(0.5).Maximum(2.0).Build()).
			Build()).
		Property("trigger_conditions", StringArraySchema("触发条件列表")).
		Property("end_conditions", StringArraySchema("结束条件列表")).
		Required("name", "description", "effect_type", "duration", "intensity", "affected_area").
		Build()
}

// floatPtr 辅助函数：创建float64指针
func floatPtr(f float64) *float64 {
	return &f
}
