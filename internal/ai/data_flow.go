package ai

import (
	"context"
	"sync"
	"time"

	"ai-text-game-iam-npc/internal/models"
	"ai-text-game-iam-npc/pkg/logger"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

/**
 * 数据流处理器
 * @description 处理AI生成数据的流式处理和实时同步
 */

// DataFlowEvent 数据流事件
type DataFlowEvent struct {
	ID        string                 `json:"id"`         // 事件ID
	Type      string                 `json:"type"`       // 事件类型
	Source    string                 `json:"source"`     // 数据源
	Target    string                 `json:"target"`     // 目标
	Data      map[string]interface{} `json:"data"`       // 事件数据
	Timestamp time.Time              `json:"timestamp"`  // 时间戳
	Metadata  map[string]interface{} `json:"metadata"`   // 元数据
}

// DataFlowProcessor 数据流处理器
type DataFlowProcessor struct {
	db          *gorm.DB
	transformer *DataTransformer
	validator   *SchemaValidator
	logger      logger.Logger
	
	// 事件通道
	eventChan   chan *DataFlowEvent
	subscribers map[string][]chan *DataFlowEvent
	mutex       sync.RWMutex
	
	// 处理器状态
	running bool
	ctx     context.Context
	cancel  context.CancelFunc
}

// NewDataFlowProcessor 创建数据流处理器
func NewDataFlowProcessor(db *gorm.DB, transformer *DataTransformer, log logger.Logger) *DataFlowProcessor {
	ctx, cancel := context.WithCancel(context.Background())
	
	processor := &DataFlowProcessor{
		db:          db,
		transformer: transformer,
		logger:      log,
		eventChan:   make(chan *DataFlowEvent, 1000),
		subscribers: make(map[string][]chan *DataFlowEvent),
		running:     false,
		ctx:         ctx,
		cancel:      cancel,
	}
	
	return processor
}

// Start 启动数据流处理器
func (dfp *DataFlowProcessor) Start() {
	if dfp.running {
		return
	}
	
	dfp.running = true
	go dfp.eventLoop()
	dfp.logger.Info("数据流处理器已启动")
}

// Stop 停止数据流处理器
func (dfp *DataFlowProcessor) Stop() {
	if !dfp.running {
		return
	}
	
	dfp.cancel()
	dfp.running = false
	dfp.logger.Info("数据流处理器已停止")
}

// eventLoop 事件循环
func (dfp *DataFlowProcessor) eventLoop() {
	for {
		select {
		case <-dfp.ctx.Done():
			return
		case event := <-dfp.eventChan:
			dfp.processEvent(event)
		}
	}
}

// PublishEvent 发布事件
func (dfp *DataFlowProcessor) PublishEvent(event *DataFlowEvent) {
	if !dfp.running {
		dfp.logger.Warn("数据流处理器未运行，忽略事件", "event_id", event.ID)
		return
	}
	
	select {
	case dfp.eventChan <- event:
		dfp.logger.Debug("事件已发布", "event_id", event.ID, "type", event.Type)
	default:
		dfp.logger.Warn("事件通道已满，丢弃事件", "event_id", event.ID, "type", event.Type)
	}
}

// processEvent 处理事件
func (dfp *DataFlowProcessor) processEvent(event *DataFlowEvent) {
	dfp.logger.Debug("处理数据流事件", "event_id", event.ID, "type", event.Type)
	
	// 根据事件类型处理
	switch event.Type {
	case "ai_content_generated":
		dfp.handleAIContentGenerated(event)
	case "scene_created":
		dfp.handleSceneCreated(event)
	case "character_created":
		dfp.handleCharacterCreated(event)
	case "event_created":
		dfp.handleEventCreated(event)
	case "validation_failed":
		dfp.handleValidationFailed(event)
	case "database_updated":
		dfp.handleDatabaseUpdated(event)
	default:
		dfp.logger.Warn("未知事件类型", "event_id", event.ID, "type", event.Type)
	}
	
	// 分发给订阅者
	dfp.distributeEvent(event)
}

// handleAIContentGenerated 处理AI内容生成事件
func (dfp *DataFlowProcessor) handleAIContentGenerated(event *DataFlowEvent) {
	contentType, ok := event.Data["content_type"].(string)
	if !ok {
		dfp.logger.Error("AI内容生成事件缺少内容类型", "event_id", event.ID)
		return
	}
	
	structuredData, ok := event.Data["structured_data"].(map[string]interface{})
	if !ok {
		dfp.logger.Error("AI内容生成事件缺少结构化数据", "event_id", event.ID)
		return
	}
	
	worldIDStr, ok := event.Data["world_id"].(string)
	if !ok {
		dfp.logger.Error("AI内容生成事件缺少世界ID", "event_id", event.ID)
		return
	}
	
	worldID, err := uuid.Parse(worldIDStr)
	if err != nil {
		dfp.logger.Error("无效的世界ID", "event_id", event.ID, "world_id", worldIDStr)
		return
	}
	
	// 根据内容类型进行相应处理
	switch contentType {
	case "scene":
		dfp.processSceneData(event.ID, structuredData, worldID)
	case "character":
		dfp.processCharacterData(event.ID, structuredData, worldID, event.Data)
	case "event":
		dfp.processEventData(event.ID, structuredData, worldID, event.Data)
	default:
		dfp.logger.Info("内容类型不需要特殊处理", "event_id", event.ID, "content_type", contentType)
	}
}

// processSceneData 处理场景数据
func (dfp *DataFlowProcessor) processSceneData(eventID string, data map[string]interface{}, worldID uuid.UUID) {
	scene, err := dfp.transformer.TransformToScene(data, worldID)
	if err != nil {
		dfp.logger.Error("场景数据转换失败", "event_id", eventID, "error", err)
		dfp.publishValidationFailedEvent(eventID, "scene", err.Error())
		return
	}
	
	// 保存到数据库
	if err := dfp.db.Create(scene).Error; err != nil {
		dfp.logger.Error("保存场景到数据库失败", "event_id", eventID, "error", err)
		return
	}
	
	// 发布场景创建事件
	dfp.publishSceneCreatedEvent(scene)
	dfp.logger.Info("场景数据处理完成", "event_id", eventID, "scene_id", scene.ID)
}

// processCharacterData 处理角色数据
func (dfp *DataFlowProcessor) processCharacterData(eventID string, data map[string]interface{}, worldID uuid.UUID, eventData map[string]interface{}) {
	var userID *uuid.UUID
	if userIDStr, ok := eventData["user_id"].(string); ok {
		if uid, err := uuid.Parse(userIDStr); err == nil {
			userID = &uid
		}
	}
	
	character, err := dfp.transformer.TransformToCharacter(data, worldID, userID)
	if err != nil {
		dfp.logger.Error("角色数据转换失败", "event_id", eventID, "error", err)
		dfp.publishValidationFailedEvent(eventID, "character", err.Error())
		return
	}
	
	// 保存到数据库
	if err := dfp.db.Create(character).Error; err != nil {
		dfp.logger.Error("保存角色到数据库失败", "event_id", eventID, "error", err)
		return
	}
	
	// 发布角色创建事件
	dfp.publishCharacterCreatedEvent(character)
	dfp.logger.Info("角色数据处理完成", "event_id", eventID, "character_id", character.ID)
}

// processEventData 处理事件数据
func (dfp *DataFlowProcessor) processEventData(eventID string, data map[string]interface{}, worldID uuid.UUID, eventData map[string]interface{}) {
	var creatorID *uuid.UUID
	if creatorIDStr, ok := eventData["creator_id"].(string); ok {
		if cid, err := uuid.Parse(creatorIDStr); err == nil {
			creatorID = &cid
		}
	}
	
	event, err := dfp.transformer.TransformToEvent(data, worldID, creatorID)
	if err != nil {
		dfp.logger.Error("事件数据转换失败", "event_id", eventID, "error", err)
		dfp.publishValidationFailedEvent(eventID, "event", err.Error())
		return
	}
	
	// 保存到数据库
	if err := dfp.db.Create(event).Error; err != nil {
		dfp.logger.Error("保存事件到数据库失败", "event_id", eventID, "error", err)
		return
	}
	
	// 发布事件创建事件
	dfp.publishEventCreatedEvent(event)
	dfp.logger.Info("事件数据处理完成", "event_id", eventID, "event_id_created", event.ID)
}

// handleSceneCreated 处理场景创建事件
func (dfp *DataFlowProcessor) handleSceneCreated(event *DataFlowEvent) {
	dfp.logger.Info("场景创建事件处理", "event_id", event.ID)
	// 可以在这里添加场景创建后的后续处理逻辑
	// 例如：更新世界状态、通知相关玩家等
}

// handleCharacterCreated 处理角色创建事件
func (dfp *DataFlowProcessor) handleCharacterCreated(event *DataFlowEvent) {
	dfp.logger.Info("角色创建事件处理", "event_id", event.ID)
	// 可以在这里添加角色创建后的后续处理逻辑
	// 例如：分配初始属性、设置默认位置等
}

// handleEventCreated 处理事件创建事件
func (dfp *DataFlowProcessor) handleEventCreated(event *DataFlowEvent) {
	dfp.logger.Info("事件创建事件处理", "event_id", event.ID)
	// 可以在这里添加事件创建后的后续处理逻辑
	// 例如：调度事件执行、通知相关角色等
}

// handleValidationFailed 处理验证失败事件
func (dfp *DataFlowProcessor) handleValidationFailed(event *DataFlowEvent) {
	dfp.logger.Warn("数据验证失败", "event_id", event.ID, "error", event.Data["error"])
	// 可以在这里添加验证失败的处理逻辑
	// 例如：记录错误、重试生成等
}

// handleDatabaseUpdated 处理数据库更新事件
func (dfp *DataFlowProcessor) handleDatabaseUpdated(event *DataFlowEvent) {
	dfp.logger.Debug("数据库更新事件处理", "event_id", event.ID)
	// 可以在这里添加数据库更新后的处理逻辑
	// 例如：清理缓存、同步到其他系统等
}

// Subscribe 订阅事件
func (dfp *DataFlowProcessor) Subscribe(eventType string) <-chan *DataFlowEvent {
	dfp.mutex.Lock()
	defer dfp.mutex.Unlock()
	
	ch := make(chan *DataFlowEvent, 100)
	dfp.subscribers[eventType] = append(dfp.subscribers[eventType], ch)
	
	dfp.logger.Debug("新的事件订阅", "event_type", eventType)
	return ch
}

// distributeEvent 分发事件给订阅者
func (dfp *DataFlowProcessor) distributeEvent(event *DataFlowEvent) {
	dfp.mutex.RLock()
	defer dfp.mutex.RUnlock()
	
	if subscribers, exists := dfp.subscribers[event.Type]; exists {
		for _, ch := range subscribers {
			select {
			case ch <- event:
				// 成功发送
			default:
				// 通道已满，跳过
				dfp.logger.Warn("订阅者通道已满，跳过事件", "event_type", event.Type)
			}
		}
	}
}

// 辅助方法：发布各种事件

// publishSceneCreatedEvent 发布场景创建事件
func (dfp *DataFlowProcessor) publishSceneCreatedEvent(scene *models.Scene) {
	event := &DataFlowEvent{
		ID:        uuid.New().String(),
		Type:      "scene_created",
		Source:    "data_flow_processor",
		Target:    "game_system",
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"scene_id":   scene.ID.String(),
			"world_id":   scene.WorldID.String(),
			"scene_name": scene.Name,
		},
		Metadata: map[string]interface{}{
			"scene_type": scene.SceneType,
			"status":     scene.Status,
		},
	}
	
	dfp.PublishEvent(event)
}

// publishCharacterCreatedEvent 发布角色创建事件
func (dfp *DataFlowProcessor) publishCharacterCreatedEvent(character *models.Character) {
	event := &DataFlowEvent{
		ID:        uuid.New().String(),
		Type:      "character_created",
		Source:    "data_flow_processor",
		Target:    "game_system",
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"character_id":   character.ID.String(),
			"world_id":       character.WorldID.String(),
			"character_name": character.Name,
		},
		Metadata: map[string]interface{}{
			"character_type": character.CharacterType,
			"status":         character.Status,
		},
	}
	
	dfp.PublishEvent(event)
}

// publishEventCreatedEvent 发布事件创建事件
func (dfp *DataFlowProcessor) publishEventCreatedEvent(gameEvent *models.Event) {
	event := &DataFlowEvent{
		ID:        uuid.New().String(),
		Type:      "event_created",
		Source:    "data_flow_processor",
		Target:    "game_system",
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"event_id":   gameEvent.ID.String(),
			"world_id":   gameEvent.WorldID.String(),
			"event_name": gameEvent.Name,
		},
		Metadata: map[string]interface{}{
			"event_type": gameEvent.EventType,
			"status":     gameEvent.Status,
			"priority":   gameEvent.Priority,
		},
	}
	
	dfp.PublishEvent(event)
}

// publishValidationFailedEvent 发布验证失败事件
func (dfp *DataFlowProcessor) publishValidationFailedEvent(originalEventID, contentType, errorMsg string) {
	event := &DataFlowEvent{
		ID:        uuid.New().String(),
		Type:      "validation_failed",
		Source:    "data_flow_processor",
		Target:    "error_handler",
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"original_event_id": originalEventID,
			"content_type":      contentType,
			"error":             errorMsg,
		},
	}
	
	dfp.PublishEvent(event)
}
