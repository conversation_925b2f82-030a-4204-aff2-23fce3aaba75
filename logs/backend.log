2025/08/05 06:30:05 🐛 [DEBUG] 开发模式已启用，详细日志输出已开启
2025/08/05 06:30:05 🐛 [DEBUG] 环境变量: ENVIRONMENT=development, DEV_ENABLE_DEBUG_LOGS=true
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] HEAD   /assets/*filepath         --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (5 handlers)
[GIN-debug] GET    /health                   --> main.setupAPIRoutes.func1 (5 handlers)
[GIN-debug] GET    /api/health               --> main.setupAPIRoutes.func1 (5 handlers)
[GIN-debug] GET    /api/v1/user/profile      --> main.getUserProfile (5 handlers)
[GIN-debug] POST   /api/v1/auth/login        --> main.mockLogin (5 handlers)
[GIN-debug] POST   /api/v1/auth/logout       --> main.mockLogout (5 handlers)
[GIN-debug] GET    /api/v1/worlds            --> main.getWorlds (5 handlers)
[GIN-debug] POST   /api/v1/worlds            --> main.createWorld (5 handlers)
[GIN-debug] GET    /api/v1/worlds/:id        --> main.getWorld (5 handlers)
[GIN-debug] GET    /api/v1/game/my-worlds    --> main.getMyWorlds (5 handlers)
[GIN-debug] GET    /api/v1/game/public-worlds --> main.getPublicWorlds (5 handlers)
[GIN-debug] POST   /api/v1/game/worlds       --> main.createWorld (5 handlers)
[GIN-debug] PUT    /api/v1/game/worlds/:id   --> main.updateWorld (5 handlers)
[GIN-debug] DELETE /api/v1/game/worlds/:id   --> main.deleteWorld (5 handlers)
[GIN-debug] GET    /api/v1/game/my-characters --> main.getMyCharacters (5 handlers)
[GIN-debug] POST   /api/v1/game/characters   --> main.createCharacter (5 handlers)
[GIN-debug] GET    /api/v1/game/characters/:id --> main.getCharacter (5 handlers)
[GIN-debug] GET    /api/v1/game/world/:worldId/characters --> main.getWorldCharacters (5 handlers)
[GIN-debug] GET    /api/v1/games/:worldId/status --> main.getGameStatus (5 handlers)
[GIN-debug] POST   /api/v1/games/:worldId/actions --> main.performAction (5 handlers)
[GIN-debug] POST   /api/v1/ai/generate/scene --> main.generateScene (5 handlers)
[GIN-debug] GET    /api/v1/ai/interactions/history --> main.getInteractionHistory (5 handlers)
[GIN-debug] GET    /api/v1/ai/stats/token-usage --> main.getTokenUsageStats (5 handlers)
[GIN-debug] GET    /api/v1/dev/test          --> main.setupAPIRoutes.func2 (5 handlers)
2025/08/05 06:30:05 🚀 简化版AI文本游戏服务器启动在端口 8080
2025/08/05 06:30:05 🌐 前端应用: http://localhost:8080
2025/08/05 06:30:05 🔧 API文档: http://localhost:8080/api/v1
2025/08/05 06:30:05 ❤️  健康检查: http://localhost:8080/health
2025/08/05 06:30:05 🐛 [DEBUG] 调试模式已启用，将显示详细的请求日志
2025/08/05 06:30:05 🐛 [DEBUG] 示例世界数据已初始化，共 3 个世界
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
[GIN-debug] Listening and serving HTTP on :8080
🌐 [2025-08-05 06:30:07] GET /health 127.0.0.1 200 33.535µs HTTP/1.1 | 用户代理: curl/7.88.1
[GIN] 2025/08/05 - 06:30:07 | 200 |      52.862µs |       127.0.0.1 | GET      "/health"
🌐 [2025-08-05 06:30:07] GET /health 127.0.0.1 200 38.404µs HTTP/1.1 | 用户代理: curl/7.88.1
[GIN] 2025/08/05 - 06:30:07 | 200 |       51.61µs |       127.0.0.1 | GET      "/health"
