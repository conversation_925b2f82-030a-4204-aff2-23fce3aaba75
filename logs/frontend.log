
> ai-text-game-frontend@0.1.0 dev:debug
> vite --logLevel info --debug

2025-08-05T06:30:07.816Z vite:config bundled config file loaded in 142.23ms
2025-08-05T06:30:07.834Z vite:config using resolved config: {
  plugins: [
    'vite:optimized-deps',
    'vite:watch-package-data',
    'vite:pre-alias',
    'alias',
    'vite:react-babel',
    'vite:react-refresh',
    'vite:react-jsx',
    'vite:modulepreload-polyfill',
    'vite:resolve',
    'vite:html-inline-proxy',
    'vite:css',
    'vite:esbuild',
    'vite:json',
    'vite:wasm-helper',
    'vite:worker',
    'vite:asset',
    'vite:wasm-fallback',
    'vite:define',
    'vite:css-post',
    'vite:worker-import-meta-url',
    'vite:asset-import-meta-url',
    'vite:dynamic-import-vars',
    'vite:import-glob',
    'vite:client-inject',
    'vite:import-analysis'
  ],
  define: {
    __DEV_MODE_ENABLED__: true,
    'process.env.DISABLE_DEV_MODE': 'false'
  },
  resolve: {
    mainFields: [ 'module', 'jsnext:main', 'jsnext' ],
    browserField: true,
    conditions: [],
    extensions: [
      '.mjs',  '.js',
      '.mts',  '.ts',
      '.jsx',  '.tsx',
      '.json'
    ],
    dedupe: [ 'react', 'react-dom' ],
    preserveSymlinks: false,
    alias: [ [Object], [Object], [Object] ]
  },
  server: {
    preTransformRequests: true,
    port: 3000,
    host: true,
    proxy: { '/api': [Object] },
    logLevel: 'info',
    sourcemapIgnoreList: [Function: isInNodeModules],
    middlewareMode: false,
    fs: { strict: true, allow: [Array], deny: [Array] }
  },
  build: {
    target: [ 'es2020', 'edge88', 'firefox78', 'chrome87', 'safari14' ],
    cssTarget: [ 'es2020', 'edge88', 'firefox78', 'chrome87', 'safari14' ],
    outDir: '../static/dist',
    assetsDir: 'assets',
    assetsInlineLimit: 4096,
    cssCodeSplit: true,
    sourcemap: true,
    rollupOptions: { output: [Object] },
    minify: 'esbuild',
    terserOptions: {},
    write: true,
    emptyOutDir: true,
    copyPublicDir: true,
    manifest: false,
    lib: false,
    ssr: false,
    ssrManifest: false,
    ssrEmitAssets: false,
    reportCompressedSize: true,
    chunkSizeWarningLimit: 500,
    watch: null,
    commonjsOptions: { include: [Array], extensions: [Array] },
    dynamicImportVarsOptions: { warnOnError: true, exclude: [Array] },
    modulePreload: { polyfill: true },
    cssMinify: true
  },
  logLevel: 'info',
  optimizeDeps: {
    disabled: 'build',
    force: undefined,
    include: [ 'react/jsx-runtime', 'react/jsx-dev-runtime', 'react' ],
    esbuildOptions: { preserveSymlinks: false }
  },
  esbuild: {
    jsxDev: true,
    jsx: 'automatic',
    jsxImportSource: undefined,
    jsxSideEffects: false
  },
  configFile: '/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/vite.config.ts',
  configFileDependencies: [
    '/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/vite.config.ts'
  ],
  inlineConfig: {
    root: undefined,
    base: undefined,
    mode: undefined,
    configFile: undefined,
    logLevel: 'info',
    clearScreen: undefined,
    optimizeDeps: { force: undefined },
    server: { host: undefined }
  },
  root: '/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend',
  base: '/',
  rawBase: '/',
  publicDir: '/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/public',
  cacheDir: '/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite',
  command: 'serve',
  mode: 'development',
  ssr: {
    format: 'esm',
    target: 'node',
    optimizeDeps: { disabled: true, esbuildOptions: [Object] }
  },
  isWorker: false,
  mainConfig: null,
  isProduction: false,
  css: undefined,
  preview: {
    port: undefined,
    strictPort: undefined,
    host: true,
    allowedHosts: undefined,
    https: undefined,
    open: undefined,
    proxy: { '/api': [Object] },
    cors: undefined,
    headers: undefined
  },
  envDir: '/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend',
  env: {
    VITE_APP_ENV: 'development',
    VITE_API_BASE_URL: 'http://localhost:8080',
    VITE_API_TIMEOUT: '30000',
    VITE_DEV_MODE: 'true',
    VITE_ENABLE_DEV_TOOLS: 'true',
    VITE_SHOW_DEV_INDICATOR: 'true',
    VITE_SKIP_AUTH: 'true',
    VITE_AUTO_LOGIN: 'true',
    VITE_DEBUG_LOGS: 'true',
    VITE_VERBOSE_LOGS: 'true',
    VITE_ENABLE_MOCK: 'false',
    VITE_MOCK_DELAY: '500',
    VITE_HMR_PORT: '3001',
    VITE_HMR_HOST: 'localhost',
    VITE_USER_NODE_ENV: 'development',
    BASE_URL: '/',
    MODE: 'development',
    DEV: true,
    PROD: false
  },
  assetsInclude: [Function: assetsInclude],
  logger: {
    hasWarned: false,
    info: [Function: info],
    warn: [Function: warn],
    warnOnce: [Function: warnOnce],
    error: [Function: error],
    clearScreen: [Function: clearScreen],
    hasErrorLogged: [Function: hasErrorLogged]
  },
  packageCache: Map(1) {
    'fnpd_/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend' => {
      dir: '/root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend',
      data: [Object],
      hasSideEffects: [Function: hasSideEffects],
      webResolvedImports: {},
      nodeResolvedImports: {},
      setResolvedCache: [Function: setResolvedCache],
      getResolvedCache: [Function: getResolvedCache]
    },
    set: [Function (anonymous)]
  },
  createResolver: [Function: createResolver],
  worker: {
    format: 'iife',
    plugins: [
      'vite:optimized-deps',
      'vite:watch-package-data',
      'vite:pre-alias',
      'alias',
      'vite:modulepreload-polyfill',
      'vite:resolve',
      'vite:html-inline-proxy',
      'vite:css',
      'vite:esbuild',
      'vite:json',
      'vite:wasm-helper',
      'vite:worker',
      'vite:asset',
      'vite:wasm-fallback',
      'vite:define',
      'vite:css-post',
      'vite:worker-import-meta-url',
      'vite:asset-import-meta-url',
      'vite:dynamic-import-vars',
      'vite:import-glob',
      'vite:client-inject',
      'vite:import-analysis'
    ],
    rollupOptions: {},
    getSortedPlugins: [Function: getSortedPlugins],
    getSortedPluginHooks: [Function: getSortedPluginHooks]
  },
  appType: 'spa',
  experimental: { importGlobRestoreExtension: false, hmrPartialAccept: false },
  webSocketToken: 'GvuJn5xqasha',
  additionalAllowedHosts: [],
  getSortedPlugins: [Function: getSortedPlugins],
  getSortedPluginHooks: [Function: getSortedPluginHooks]
}
2025-08-05T06:30:07.845Z vite:deps Hash is consistent. Skipping. Use --force to override.

  VITE v4.5.14  ready in 272 ms

  ➜  Local:   http://localhost:3000/
  ➜  Network: http://*************:3000/
  ➜  press h to show help
2025-08-05T06:30:07.848Z vite:esbuild 17.05ms tsconfck init /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend
2025-08-05T06:30:09.500Z vite:html-fallback Rewriting GET / to /index.html
2025-08-05T06:30:09.517Z vite:resolve 1.12ms /src/main.tsx -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/main.tsx
2025-08-05T06:30:09.517Z vite:resolve 0.90ms /index.html?html-proxy&direct&index=0.css -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/index.html?html-proxy&direct&index=0.css
2025-08-05T06:30:09.521Z vite:import-analysis [skipped] index.html?html-proxy&direct&index=0.css
2025-08-05T06:30:09.522Z vite:time 25.14ms /index.html
2025-08-05T06:30:09.524Z vite:load 5.42ms [fs] /src/main.tsx
2025-08-05T06:30:09.576Z vite:resolve 0.33ms react/jsx-dev-runtime ->  react/jsx-dev-runtime
2025-08-05T06:30:09.577Z vite:resolve 1.27ms ./store -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/index.ts
2025-08-05T06:30:09.577Z vite:resolve 1.30ms ./App -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/App.tsx
2025-08-05T06:30:09.577Z vite:resolve 1.32ms ./components/AppInitializer -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/components/AppInitializer.tsx
2025-08-05T06:30:09.577Z vite:resolve 1.33ms ./styles/index.css -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/styles/index.css
2025-08-05T06:30:09.577Z vite:resolve 1.56ms react -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T06:30:09.578Z vite:resolve 1.56ms react-dom/client -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react-dom_client.js?v=5981b4ff
2025-08-05T06:30:09.578Z vite:resolve 1.57ms react-redux -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react-redux.js?v=5981b4ff
2025-08-05T06:30:09.578Z vite:resolve 1.58ms react-router-dom -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T06:30:09.578Z vite:resolve 1.59ms antd -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T06:30:09.578Z vite:resolve 1.61ms antd/locale/zh_CN -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/antd_locale_zh_CN.js?v=5981b4ff
2025-08-05T06:30:09.578Z vite:load 0.99ms [plugin]  react/jsx-dev-runtime
2025-08-05T06:30:09.578Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T06:30:09.581Z vite:import-analysis /node_modules/.vite/deps/react-dom_client.js?v=5981b4ff needs interop
2025-08-05T06:30:09.581Z vite:import-analysis /node_modules/.vite/deps/antd_locale_zh_CN.js?v=5981b4ff needs interop
2025-08-05T06:30:09.581Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react.js
2025-08-05T06:30:09.581Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react-dom_client.js
2025-08-05T06:30:09.581Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react-redux.js
2025-08-05T06:30:09.581Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react-router-dom.js
2025-08-05T06:30:09.582Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/antd.js
2025-08-05T06:30:09.582Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/antd_locale_zh_CN.js
2025-08-05T06:30:09.582Z vite:import-analysis 7.26ms [11 imports rewritten] src/main.tsx
2025-08-05T06:30:09.584Z vite:transform 59.86ms /src/main.tsx
2025-08-05T06:30:09.585Z vite:resolve 0.17ms react/jsx-dev-runtime -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=5981b4ff
2025-08-05T06:30:09.585Z vite:import-analysis /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=5981b4ff needs interop
2025-08-05T06:30:09.586Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/react_jsx-dev-runtime.js
2025-08-05T06:30:09.586Z vite:import-analysis 0.85ms [1 imports rewritten]  react/jsx-dev-runtime
2025-08-05T06:30:09.586Z vite:transform 7.16ms  react/jsx-dev-runtime
2025-08-05T06:30:09.588Z vite:load 6.38ms [plugin] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T06:30:09.588Z vite:resolve 0.17ms ./chunk-LXGCQ6UQ.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T06:30:09.588Z vite:resolve 0.19ms ./chunk-ROME4SDB.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.588Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-LXGCQ6UQ.js
2025-08-05T06:30:09.588Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-ROME4SDB.js
2025-08-05T06:30:09.588Z vite:import-analysis 0.70ms [2 imports rewritten] node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T06:30:09.588Z vite:transform 0.85ms /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T06:30:09.589Z vite:load 7.31ms [plugin] /node_modules/.vite/deps/react-dom_client.js?v=5981b4ff
2025-08-05T06:30:09.589Z vite:resolve 0.10ms ./chunk-ZRNALROW.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-ZRNALROW.js?v=5981b4ff
2025-08-05T06:30:09.589Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-ZRNALROW.js
2025-08-05T06:30:09.589Z vite:import-analysis 0.51ms [3 imports rewritten] node_modules/.vite/deps/react-dom_client.js?v=5981b4ff
2025-08-05T06:30:09.589Z vite:transform 0.61ms /node_modules/.vite/deps/react-dom_client.js?v=5981b4ff
2025-08-05T06:30:09.589Z vite:load 8.02ms [plugin] /node_modules/.vite/deps/react-redux.js?v=5981b4ff
2025-08-05T06:30:09.590Z vite:resolve 0.15ms ./chunk-J2UTGV2I.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-J2UTGV2I.js?v=5981b4ff
2025-08-05T06:30:09.590Z vite:resolve 0.17ms ./chunk-HJ4OHFNV.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-HJ4OHFNV.js?v=5981b4ff
2025-08-05T06:30:09.590Z vite:resolve 0.19ms ./chunk-SNXB62YR.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T06:30:09.590Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-J2UTGV2I.js
2025-08-05T06:30:09.590Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-HJ4OHFNV.js
2025-08-05T06:30:09.590Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-SNXB62YR.js
2025-08-05T06:30:09.590Z vite:import-analysis 0.73ms [6 imports rewritten] node_modules/.vite/deps/react-redux.js?v=5981b4ff
2025-08-05T06:30:09.590Z vite:transform 0.83ms /node_modules/.vite/deps/react-redux.js?v=5981b4ff
2025-08-05T06:30:09.590Z vite:load 8.85ms [plugin] /node_modules/.vite/deps/antd_locale_zh_CN.js?v=5981b4ff
2025-08-05T06:30:09.591Z vite:import-analysis 0.56ms [1 imports rewritten] node_modules/.vite/deps/antd_locale_zh_CN.js?v=5981b4ff
2025-08-05T06:30:09.591Z vite:transform 0.68ms /node_modules/.vite/deps/antd_locale_zh_CN.js?v=5981b4ff
2025-08-05T06:30:09.591Z vite:load 12.68ms [fs] /src/store/index.ts
2025-08-05T06:30:09.591Z vite:load 10.07ms [plugin] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T06:30:09.594Z vite:import-analysis 1.77ms [3 imports rewritten] node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T06:30:09.594Z vite:transform 2.26ms /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T06:30:09.594Z vite:load 15.96ms [fs] /src/App.tsx
2025-08-05T06:30:09.613Z vite:load 34.52ms [fs] /src/components/AppInitializer.tsx
2025-08-05T06:30:09.623Z vite:load 44.55ms [fs] /src/styles/index.css
2025-08-05T06:30:09.623Z vite:hmr [self-accepts] src/styles/index.css
2025-08-05T06:30:09.624Z vite:import-analysis 0.27ms [0 imports rewritten] src/styles/index.css
2025-08-05T06:30:09.624Z vite:transform 0.71ms /src/styles/index.css
2025-08-05T06:30:09.625Z vite:load 39.56ms [plugin] /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=5981b4ff
2025-08-05T06:30:09.626Z vite:import-analysis 0.63ms [2 imports rewritten] node_modules/.vite/deps/react_jsx-dev-runtime.js?v=5981b4ff
2025-08-05T06:30:09.626Z vite:transform 1.08ms /node_modules/.vite/deps/react_jsx-dev-runtime.js?v=5981b4ff
2025-08-05T06:30:09.629Z vite:resolve 1.80ms ./slices/authSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/authSlice.ts
2025-08-05T06:30:09.629Z vite:resolve 1.83ms ./slices/gameSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/gameSlice.ts
2025-08-05T06:30:09.629Z vite:resolve 1.84ms ./slices/uiSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/uiSlice.ts
2025-08-05T06:30:09.629Z vite:resolve 1.85ms ./api/apiSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/apiSlice.ts
2025-08-05T06:30:09.629Z vite:resolve 1.86ms ./hooks -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/hooks.ts
2025-08-05T06:30:09.629Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T06:30:09.629Z vite:resolve 1.77ms ./pages/LoginPage -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/pages/LoginPage.tsx
2025-08-05T06:30:09.629Z vite:resolve 1.79ms ./pages/AuthCallbackPage -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/pages/AuthCallbackPage.tsx
2025-08-05T06:30:09.629Z vite:resolve 1.80ms ./pages/GameLobbyPage -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/pages/GameLobbyPage.tsx
2025-08-05T06:30:09.629Z vite:resolve 1.81ms ./pages/WorldCreatePage -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/pages/WorldCreatePage.tsx
2025-08-05T06:30:09.629Z vite:resolve 1.82ms ./pages/GamePage -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/pages/GamePage.tsx
2025-08-05T06:30:09.629Z vite:resolve 1.82ms ./pages/ProfilePage -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/pages/ProfilePage.tsx
2025-08-05T06:30:09.629Z vite:resolve 1.83ms ./components/layout/AppHeader -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/components/layout/AppHeader.tsx
2025-08-05T06:30:09.629Z vite:resolve 1.84ms ./components/layout/AppFooter -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/components/layout/AppFooter.tsx
2025-08-05T06:30:09.629Z vite:resolve 1.85ms ./components/DevModeIndicator -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/components/DevModeIndicator.tsx
2025-08-05T06:30:09.629Z vite:resolve 1.64ms ../services/authService -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/services/authService.ts
2025-08-05T06:30:09.629Z vite:resolve 2.12ms @reduxjs/toolkit -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@reduxjs_toolkit.js?v=5981b4ff
2025-08-05T06:30:09.629Z vite:resolve 2.13ms @reduxjs/toolkit/query -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@reduxjs_toolkit_query.js?v=5981b4ff
2025-08-05T06:30:09.630Z vite:resolve 2.00ms styled-components -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T06:30:09.630Z vite:resolve 2.02ms framer-motion -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/framer-motion.js?v=5981b4ff
2025-08-05T06:30:09.630Z vite:load 0.39ms [plugin] /@react-refresh
2025-08-05T06:30:09.630Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T06:30:09.630Z vite:cache [memory] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T06:30:09.630Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T06:30:09.631Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@reduxjs_toolkit.js
2025-08-05T06:30:09.631Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@reduxjs_toolkit_query.js
2025-08-05T06:30:09.631Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/styled-components.js
2025-08-05T06:30:09.631Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/framer-motion.js
2025-08-05T06:30:09.631Z vite:hmr [self-accepts] src/App.tsx
2025-08-05T06:30:09.631Z vite:hmr [self-accepts] src/components/AppInitializer.tsx
2025-08-05T06:30:09.631Z vite:import-analysis 3.59ms [7 imports rewritten] src/store/index.ts
2025-08-05T06:30:09.631Z vite:import-analysis 3.52ms [15 imports rewritten] src/App.tsx
2025-08-05T06:30:09.631Z vite:import-analysis 3.53ms [6 imports rewritten] src/components/AppInitializer.tsx
2025-08-05T06:30:09.631Z vite:transform 40.00ms /src/store/index.ts
2025-08-05T06:30:09.633Z vite:transform 38.50ms /src/App.tsx
2025-08-05T06:30:09.633Z vite:transform 19.96ms /src/components/AppInitializer.tsx
2025-08-05T06:30:09.633Z vite:import-analysis 0.16ms [no imports] /@react-refresh
2025-08-05T06:30:09.633Z vite:transform 3.50ms /@react-refresh
2025-08-05T06:30:09.637Z vite:load 48.32ms [plugin] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T06:30:09.637Z vite:import-analysis 0.57ms [1 imports rewritten] node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T06:30:09.637Z vite:transform 0.80ms /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T06:30:09.637Z vite:load 49.27ms [plugin] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.638Z vite:import-analysis 0.02ms [no imports] node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.638Z vite:transform 0.14ms /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.638Z vite:load 47.78ms [plugin] /node_modules/.vite/deps/chunk-J2UTGV2I.js?v=5981b4ff
2025-08-05T06:30:09.638Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T06:30:09.638Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.638Z vite:import-analysis 0.60ms [5 imports rewritten] node_modules/.vite/deps/chunk-J2UTGV2I.js?v=5981b4ff
2025-08-05T06:30:09.638Z vite:transform 0.73ms /node_modules/.vite/deps/chunk-J2UTGV2I.js?v=5981b4ff
2025-08-05T06:30:09.639Z vite:load 49.53ms [plugin] /node_modules/.vite/deps/chunk-HJ4OHFNV.js?v=5981b4ff
2025-08-05T06:30:09.640Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.640Z vite:import-analysis 0.18ms [1 imports rewritten] node_modules/.vite/deps/chunk-HJ4OHFNV.js?v=5981b4ff
2025-08-05T06:30:09.640Z vite:transform 0.26ms /node_modules/.vite/deps/chunk-HJ4OHFNV.js?v=5981b4ff
2025-08-05T06:30:09.640Z vite:load 49.85ms [plugin] /node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T06:30:09.640Z vite:import-analysis 0.01ms [no imports] node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T06:30:09.640Z vite:transform 0.09ms /node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T06:30:09.642Z vite:load 11.49ms [plugin] /node_modules/.vite/deps/@reduxjs_toolkit.js?v=5981b4ff
2025-08-05T06:30:09.642Z vite:resolve 0.09ms ./chunk-GBXKZDP3.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-GBXKZDP3.js?v=5981b4ff
2025-08-05T06:30:09.642Z vite:resolve 0.11ms ./chunk-5F26ILMS.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T06:30:09.642Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.642Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-GBXKZDP3.js
2025-08-05T06:30:09.642Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-5F26ILMS.js
2025-08-05T06:30:09.643Z vite:import-analysis 0.44ms [3 imports rewritten] node_modules/.vite/deps/@reduxjs_toolkit.js?v=5981b4ff
2025-08-05T06:30:09.643Z vite:transform 0.52ms /node_modules/.vite/deps/@reduxjs_toolkit.js?v=5981b4ff
2025-08-05T06:30:09.643Z vite:load 12.06ms [plugin] /node_modules/.vite/deps/@reduxjs_toolkit_query.js?v=5981b4ff
2025-08-05T06:30:09.643Z vite:resolve 0.10ms ./chunk-4UNUQYEM.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-4UNUQYEM.js?v=5981b4ff
2025-08-05T06:30:09.643Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.643Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-4UNUQYEM.js
2025-08-05T06:30:09.643Z vite:import-analysis 0.39ms [4 imports rewritten] node_modules/.vite/deps/@reduxjs_toolkit_query.js?v=5981b4ff
2025-08-05T06:30:09.643Z vite:transform 0.46ms /node_modules/.vite/deps/@reduxjs_toolkit_query.js?v=5981b4ff
2025-08-05T06:30:09.643Z vite:load 12.42ms [plugin] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T06:30:09.644Z vite:resolve 0.09ms ./chunk-JR5FH6E4.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-JR5FH6E4.js?v=5981b4ff
2025-08-05T06:30:09.644Z vite:cache [memory] /node_modules/.vite/deps/chunk-HJ4OHFNV.js?v=5981b4ff
2025-08-05T06:30:09.644Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T06:30:09.644Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.644Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-JR5FH6E4.js
2025-08-05T06:30:09.644Z vite:import-analysis 0.68ms [4 imports rewritten] node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T06:30:09.644Z vite:transform 0.85ms /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T06:30:09.644Z vite:load 13.39ms [plugin] /node_modules/.vite/deps/framer-motion.js?v=5981b4ff
2025-08-05T06:30:09.647Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T06:30:09.647Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.647Z vite:import-analysis 2.57ms [2 imports rewritten] node_modules/.vite/deps/framer-motion.js?v=5981b4ff
2025-08-05T06:30:09.647Z vite:transform 3.26ms /node_modules/.vite/deps/framer-motion.js?v=5981b4ff
2025-08-05T06:30:09.648Z vite:load 17.99ms [fs] /src/store/slices/authSlice.ts
2025-08-05T06:30:09.648Z vite:load 18.29ms [fs] /src/store/slices/gameSlice.ts
2025-08-05T06:30:09.648Z vite:load 18.49ms [fs] /src/store/slices/uiSlice.ts
2025-08-05T06:30:09.649Z vite:load 59.53ms [plugin] /node_modules/.vite/deps/chunk-ZRNALROW.js?v=5981b4ff
2025-08-05T06:30:09.657Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T06:30:09.658Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.658Z vite:import-analysis 6.19ms [2 imports rewritten] node_modules/.vite/deps/chunk-ZRNALROW.js?v=5981b4ff
2025-08-05T06:30:09.658Z vite:transform 8.93ms /node_modules/.vite/deps/chunk-ZRNALROW.js?v=5981b4ff
2025-08-05T06:30:09.660Z vite:load 30.48ms [fs] /src/store/hooks.ts
2025-08-05T06:30:09.661Z vite:load 30.97ms [fs] /src/store/api/apiSlice.ts
2025-08-05T06:30:09.661Z vite:load 31.09ms [fs] /src/pages/LoginPage.tsx
2025-08-05T06:30:09.680Z vite:load 50.48ms [fs] /src/pages/AuthCallbackPage.tsx
2025-08-05T06:30:09.704Z vite:load 74.46ms [fs] /src/pages/GameLobbyPage.tsx
2025-08-05T06:30:09.725Z vite:load 95.46ms [fs] /src/pages/WorldCreatePage.tsx
2025-08-05T06:30:09.746Z vite:load 116.40ms [fs] /src/pages/GamePage.tsx
2025-08-05T06:30:09.788Z vite:load 157.93ms [fs] /src/components/layout/AppHeader.tsx
2025-08-05T06:30:09.798Z vite:load 168.48ms [fs] /src/pages/ProfilePage.tsx
2025-08-05T06:30:09.816Z vite:load 186.22ms [fs] /src/components/layout/AppFooter.tsx
2025-08-05T06:30:09.820Z vite:load 190.25ms [fs] /src/components/DevModeIndicator.tsx
2025-08-05T06:30:09.837Z vite:load 206.58ms [fs] /src/services/authService.ts
2025-08-05T06:30:09.841Z vite:cache [memory] /@react-refresh
2025-08-05T06:30:09.841Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T06:30:09.841Z vite:resolve 0.87ms ../store/hooks -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/hooks.ts
2025-08-05T06:30:09.841Z vite:resolve 0.89ms ../store/slices/authSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/authSlice.ts
2025-08-05T06:30:09.841Z vite:resolve 0.90ms ../store/slices/uiSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/uiSlice.ts
2025-08-05T06:30:09.841Z vite:resolve 1.23ms @reduxjs/toolkit/query/react -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@reduxjs_toolkit_query_react.js?v=5981b4ff
2025-08-05T06:30:09.841Z vite:resolve 0.97ms @ant-design/icons -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@ant-design_icons.js?v=5981b4ff
2025-08-05T06:30:09.842Z vite:cache [memory] /node_modules/.vite/deps/@reduxjs_toolkit.js?v=5981b4ff
2025-08-05T06:30:09.842Z vite:cache [memory] /node_modules/.vite/deps/react-redux.js?v=5981b4ff
2025-08-05T06:30:09.842Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T06:30:09.842Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T06:30:09.842Z vite:cache [memory] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T06:30:09.842Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T06:30:09.842Z vite:cache [memory] /node_modules/.vite/deps/framer-motion.js?v=5981b4ff
2025-08-05T06:30:09.842Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@reduxjs_toolkit_query_react.js
2025-08-05T06:30:09.842Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/@ant-design_icons.js
2025-08-05T06:30:09.842Z vite:hmr [self-accepts] src/pages/LoginPage.tsx
2025-08-05T06:30:09.842Z vite:import-analysis 2.54ms [1 imports rewritten] src/store/slices/authSlice.ts
2025-08-05T06:30:09.842Z vite:import-analysis 2.55ms [1 imports rewritten] src/store/slices/uiSlice.ts
2025-08-05T06:30:09.842Z vite:import-analysis 2.56ms [1 imports rewritten] src/store/slices/gameSlice.ts
2025-08-05T06:30:09.842Z vite:import-analysis 2.56ms [1 imports rewritten] src/store/hooks.ts
2025-08-05T06:30:09.842Z vite:import-analysis 2.58ms [1 imports rewritten] src/store/api/apiSlice.ts
2025-08-05T06:30:09.842Z vite:import-analysis 2.26ms [12 imports rewritten] src/pages/LoginPage.tsx
2025-08-05T06:30:09.842Z vite:transform 194.60ms /src/store/slices/authSlice.ts
2025-08-05T06:30:09.842Z vite:transform 194.14ms /src/store/slices/uiSlice.ts
2025-08-05T06:30:09.843Z vite:transform 194.39ms /src/store/slices/gameSlice.ts
2025-08-05T06:30:09.843Z vite:transform 182.20ms /src/store/hooks.ts
2025-08-05T06:30:09.843Z vite:transform 181.77ms /src/store/api/apiSlice.ts
2025-08-05T06:30:09.845Z vite:transform 183.37ms /src/pages/LoginPage.tsx
2025-08-05T06:30:09.847Z vite:cache [memory] /@react-refresh
2025-08-05T06:30:09.847Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T06:30:09.847Z vite:resolve 1.16ms ../store/slices/gameSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/gameSlice.ts
2025-08-05T06:30:09.847Z vite:resolve 1.19ms ../store/api -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/index.ts
2025-08-05T06:30:09.847Z vite:resolve 1.20ms ../components/WorldDetailModal -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/components/WorldDetailModal.tsx
2025-08-05T06:30:09.847Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T06:30:09.848Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T06:30:09.848Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T06:30:09.848Z vite:cache [memory] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T06:30:09.848Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T06:30:09.848Z vite:cache [memory] /node_modules/.vite/deps/framer-motion.js?v=5981b4ff
2025-08-05T06:30:09.848Z vite:hmr [self-accepts] src/pages/AuthCallbackPage.tsx
2025-08-05T06:30:09.848Z vite:hmr [self-accepts] src/pages/GameLobbyPage.tsx
2025-08-05T06:30:09.848Z vite:import-analysis 2.90ms [11 imports rewritten] src/pages/AuthCallbackPage.tsx
2025-08-05T06:30:09.848Z vite:import-analysis 2.91ms [13 imports rewritten] src/pages/GameLobbyPage.tsx
2025-08-05T06:30:09.852Z vite:transform 171.59ms /src/pages/AuthCallbackPage.tsx
2025-08-05T06:30:09.852Z vite:transform 147.62ms /src/pages/GameLobbyPage.tsx
2025-08-05T06:30:09.856Z vite:cache [memory] /@react-refresh
2025-08-05T06:30:09.856Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T06:30:09.856Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T06:30:09.857Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T06:30:09.857Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T06:30:09.857Z vite:cache [memory] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T06:30:09.857Z vite:hmr [self-accepts] src/pages/WorldCreatePage.tsx
2025-08-05T06:30:09.857Z vite:import-analysis 1.80ms [10 imports rewritten] src/pages/WorldCreatePage.tsx
2025-08-05T06:30:09.858Z vite:transform 132.08ms /src/pages/WorldCreatePage.tsx
2025-08-05T06:30:09.860Z vite:cache [memory] /@react-refresh
2025-08-05T06:30:09.860Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T06:30:09.860Z vite:resolve 0.65ms ../components/CharacterSelectionModal -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/components/CharacterSelectionModal.tsx
2025-08-05T06:30:09.860Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T06:30:09.860Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T06:30:09.860Z vite:cache [memory] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T06:30:09.860Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T06:30:09.860Z vite:hmr [self-accepts] src/pages/GamePage.tsx
2025-08-05T06:30:09.860Z vite:import-analysis 1.70ms [11 imports rewritten] src/pages/GamePage.tsx
2025-08-05T06:30:09.862Z vite:transform 115.18ms /src/pages/GamePage.tsx
2025-08-05T06:30:09.862Z vite:load 280.73ms [plugin] /node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T06:30:09.899Z vite:resolve 0.22ms ./chunk-WPFDAWNF.js -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-WPFDAWNF.js?v=5981b4ff
2025-08-05T06:30:09.899Z vite:cache [memory] /node_modules/.vite/deps/chunk-ZRNALROW.js?v=5981b4ff
2025-08-05T06:30:09.899Z vite:cache [memory] /node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T06:30:09.899Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T06:30:09.899Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.899Z vite:optimize-deps load /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/node_modules/.vite/deps/chunk-WPFDAWNF.js
2025-08-05T06:30:09.899Z vite:import-analysis 26.68ms [7 imports rewritten] node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T06:30:09.899Z vite:transform 37.10ms /node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T06:30:09.913Z vite:cache [memory] /@react-refresh
2025-08-05T06:30:09.913Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T06:30:09.914Z vite:resolve 0.65ms ../../store/hooks -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/hooks.ts
2025-08-05T06:30:09.914Z vite:resolve 0.66ms ../../store/slices/authSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/authSlice.ts
2025-08-05T06:30:09.914Z vite:resolve 0.68ms ../../store/slices/uiSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/slices/uiSlice.ts
2025-08-05T06:30:09.914Z vite:cache [memory] /src/store/hooks.ts
2025-08-05T06:30:09.914Z vite:cache [memory] /src/store/slices/authSlice.ts
2025-08-05T06:30:09.914Z vite:cache [memory] /src/store/slices/uiSlice.ts
2025-08-05T06:30:09.914Z vite:cache [memory] /node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T06:30:09.914Z vite:cache [memory] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T06:30:09.914Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T06:30:09.914Z vite:hmr [self-accepts] src/components/layout/AppHeader.tsx
2025-08-05T06:30:09.914Z vite:import-analysis 1.15ms [9 imports rewritten] src/components/layout/AppHeader.tsx
2025-08-05T06:30:09.914Z vite:transform 126.20ms /src/components/layout/AppHeader.tsx
2025-08-05T06:30:09.917Z vite:cache [memory] /@react-refresh
2025-08-05T06:30:09.917Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T06:30:09.917Z vite:resolve 1.27ms ../services -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/services/index.ts
2025-08-05T06:30:09.917Z vite:cache [memory] /src/store/hooks.ts
2025-08-05T06:30:09.917Z vite:cache [memory] /src/store/slices/uiSlice.ts
2025-08-05T06:30:09.917Z vite:cache [memory] /src/store/slices/authSlice.ts
2025-08-05T06:30:09.917Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T06:30:09.917Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T06:30:09.918Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T06:30:09.918Z vite:cache [memory] /node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T06:30:09.918Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T06:30:09.918Z vite:cache [memory] /node_modules/.vite/deps/react-router-dom.js?v=5981b4ff
2025-08-05T06:30:09.918Z vite:hmr [self-accepts] src/pages/ProfilePage.tsx
2025-08-05T06:30:09.918Z vite:hmr [self-accepts] src/components/layout/AppFooter.tsx
2025-08-05T06:30:09.918Z vite:hmr [self-accepts] src/components/DevModeIndicator.tsx
2025-08-05T06:30:09.918Z vite:import-analysis 2.39ms [11 imports rewritten] src/pages/ProfilePage.tsx
2025-08-05T06:30:09.918Z vite:import-analysis 2.41ms [5 imports rewritten] src/components/layout/AppFooter.tsx
2025-08-05T06:30:09.918Z vite:import-analysis 2.42ms [9 imports rewritten] src/components/DevModeIndicator.tsx
2025-08-05T06:30:09.919Z vite:transform 120.16ms /src/pages/ProfilePage.tsx
2025-08-05T06:30:09.919Z vite:transform 102.52ms /src/components/layout/AppFooter.tsx
2025-08-05T06:30:09.919Z vite:transform 98.50ms /src/components/DevModeIndicator.tsx
2025-08-05T06:30:09.920Z vite:resolve 0.21ms ../store -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/index.ts
2025-08-05T06:30:09.920Z vite:cache [memory] /src/store/index.ts
2025-08-05T06:30:09.920Z vite:cache [memory] /src/store/slices/authSlice.ts
2025-08-05T06:30:09.920Z vite:import-analysis 0.55ms [2 imports rewritten] src/services/authService.ts
2025-08-05T06:30:09.920Z vite:transform 82.70ms /src/services/authService.ts
2025-08-05T06:30:09.923Z vite:load 280.20ms [plugin] /node_modules/.vite/deps/chunk-GBXKZDP3.js?v=5981b4ff
2025-08-05T06:30:09.924Z vite:import-analysis 1.30ms [1 imports rewritten] node_modules/.vite/deps/chunk-GBXKZDP3.js?v=5981b4ff
2025-08-05T06:30:09.924Z vite:transform 1.65ms /node_modules/.vite/deps/chunk-GBXKZDP3.js?v=5981b4ff
2025-08-05T06:30:09.924Z vite:load 282.05ms [plugin] /node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T06:30:09.925Z vite:import-analysis 0.03ms [no imports] node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T06:30:09.925Z vite:transform 0.12ms /node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T06:30:09.925Z vite:load 281.69ms [plugin] /node_modules/.vite/deps/chunk-4UNUQYEM.js?v=5981b4ff
2025-08-05T06:30:09.926Z vite:cache [memory] /node_modules/.vite/deps/chunk-GBXKZDP3.js?v=5981b4ff
2025-08-05T06:30:09.926Z vite:import-analysis 0.98ms [1 imports rewritten] node_modules/.vite/deps/chunk-4UNUQYEM.js?v=5981b4ff
2025-08-05T06:30:09.926Z vite:transform 1.19ms /node_modules/.vite/deps/chunk-4UNUQYEM.js?v=5981b4ff
2025-08-05T06:30:09.926Z vite:load 282.10ms [plugin] /node_modules/.vite/deps/chunk-JR5FH6E4.js?v=5981b4ff
2025-08-05T06:30:09.926Z vite:import-analysis 0.02ms [no imports] node_modules/.vite/deps/chunk-JR5FH6E4.js?v=5981b4ff
2025-08-05T06:30:09.926Z vite:transform 0.08ms /node_modules/.vite/deps/chunk-JR5FH6E4.js?v=5981b4ff
2025-08-05T06:30:09.927Z vite:load 84.42ms [plugin] /node_modules/.vite/deps/@reduxjs_toolkit_query_react.js?v=5981b4ff
2025-08-05T06:30:09.927Z vite:cache [memory] /node_modules/.vite/deps/chunk-J2UTGV2I.js?v=5981b4ff
2025-08-05T06:30:09.927Z vite:cache [memory] /node_modules/.vite/deps/chunk-ZRNALROW.js?v=5981b4ff
2025-08-05T06:30:09.928Z vite:cache [memory] /node_modules/.vite/deps/chunk-HJ4OHFNV.js?v=5981b4ff
2025-08-05T06:30:09.928Z vite:cache [memory] /node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T06:30:09.928Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T06:30:09.928Z vite:cache [memory] /node_modules/.vite/deps/chunk-4UNUQYEM.js?v=5981b4ff
2025-08-05T06:30:09.928Z vite:cache [memory] /node_modules/.vite/deps/chunk-GBXKZDP3.js?v=5981b4ff
2025-08-05T06:30:09.928Z vite:cache [memory] /node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T06:30:09.928Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.928Z vite:import-analysis 0.92ms [9 imports rewritten] node_modules/.vite/deps/@reduxjs_toolkit_query_react.js?v=5981b4ff
2025-08-05T06:30:09.928Z vite:transform 1.02ms /node_modules/.vite/deps/@reduxjs_toolkit_query_react.js?v=5981b4ff
2025-08-05T06:30:09.928Z vite:load 85.44ms [plugin] /node_modules/.vite/deps/@ant-design_icons.js?v=5981b4ff
2025-08-05T06:30:09.929Z vite:cache [memory] /node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T06:30:09.929Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T06:30:09.929Z vite:cache [memory] /node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T06:30:09.929Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.929Z vite:import-analysis 1.39ms [5 imports rewritten] node_modules/.vite/deps/@ant-design_icons.js?v=5981b4ff
2025-08-05T06:30:09.929Z vite:transform 1.56ms /node_modules/.vite/deps/@ant-design_icons.js?v=5981b4ff
2025-08-05T06:30:09.929Z vite:load 81.22ms [fs] /src/store/api/index.ts
2025-08-05T06:30:09.930Z vite:load 81.47ms [fs] /src/components/WorldDetailModal.tsx
2025-08-05T06:30:09.938Z vite:load 77.31ms [fs] /src/components/CharacterSelectionModal.tsx
2025-08-05T06:30:09.944Z vite:load 26.26ms [fs] /src/services/index.ts
2025-08-05T06:30:09.945Z vite:resolve 0.74ms ./apiSlice -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/apiSlice.ts
2025-08-05T06:30:09.945Z vite:resolve 0.77ms ./authApi -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/authApi.ts
2025-08-05T06:30:09.945Z vite:resolve 0.78ms ./worldApi -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/worldApi.ts
2025-08-05T06:30:09.945Z vite:resolve 0.79ms ./characterApi -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/characterApi.ts
2025-08-05T06:30:09.945Z vite:resolve 0.79ms ./gameInteractionApi -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/gameInteractionApi.ts
2025-08-05T06:30:09.945Z vite:resolve 0.81ms ./aiApi -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/store/api/aiApi.ts
2025-08-05T06:30:09.945Z vite:cache [memory] /@react-refresh
2025-08-05T06:30:09.945Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T06:30:09.945Z vite:cache [memory] /src/store/api/apiSlice.ts
2025-08-05T06:30:09.945Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T06:30:09.946Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T06:30:09.946Z vite:cache [memory] /node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T06:30:09.946Z vite:cache [memory] /node_modules/.vite/deps/@ant-design_icons.js?v=5981b4ff
2025-08-05T06:30:09.946Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T06:30:09.946Z vite:hmr [self-accepts] src/components/WorldDetailModal.tsx
2025-08-05T06:30:09.946Z vite:import-analysis 1.44ms [6 imports rewritten] src/store/api/index.ts
2025-08-05T06:30:09.946Z vite:import-analysis 1.35ms [7 imports rewritten] src/components/WorldDetailModal.tsx
2025-08-05T06:30:09.946Z vite:transform 16.42ms /src/store/api/index.ts
2025-08-05T06:30:09.946Z vite:transform 16.44ms /src/components/WorldDetailModal.tsx
2025-08-05T06:30:09.948Z vite:cache [memory] /@react-refresh
2025-08-05T06:30:09.948Z vite:resolve 0.53ms ./authService -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/services/authService.ts
2025-08-05T06:30:09.948Z vite:resolve 0.55ms ./gameService -> /root/workspace/git.atjog.com/aier/ai-text-game-iam-npc/web/frontend/src/services/gameService.ts
2025-08-05T06:30:09.948Z vite:cache [memory]  react/jsx-dev-runtime
2025-08-05T06:30:09.948Z vite:cache [memory] /src/services/authService.ts
2025-08-05T06:30:09.948Z vite:cache [memory] /src/store/api/index.ts
2025-08-05T06:30:09.948Z vite:import-analysis /node_modules/.vite/deps/react.js?v=5981b4ff needs interop
2025-08-05T06:30:09.949Z vite:cache [memory] /node_modules/.vite/deps/react.js?v=5981b4ff
2025-08-05T06:30:09.949Z vite:cache [memory] /node_modules/.vite/deps/antd.js?v=5981b4ff
2025-08-05T06:30:09.949Z vite:cache [memory] /node_modules/.vite/deps/@ant-design_icons.js?v=5981b4ff
2025-08-05T06:30:09.949Z vite:cache [memory] /node_modules/.vite/deps/styled-components.js?v=5981b4ff
2025-08-05T06:30:09.949Z vite:hmr [self-accepts] src/components/CharacterSelectionModal.tsx
2025-08-05T06:30:09.949Z vite:import-analysis 1.04ms [2 imports rewritten] src/services/index.ts
2025-08-05T06:30:09.949Z vite:import-analysis 1.05ms [7 imports rewritten] src/components/CharacterSelectionModal.tsx
2025-08-05T06:30:09.949Z vite:transform 4.88ms /src/services/index.ts
2025-08-05T06:30:09.949Z vite:transform 11.28ms /src/components/CharacterSelectionModal.tsx
2025-08-05T06:30:09.949Z vite:load 49.97ms [plugin] /node_modules/.vite/deps/chunk-WPFDAWNF.js?v=5981b4ff
2025-08-05T06:30:09.958Z vite:cache [memory] /node_modules/.vite/deps/chunk-SNXB62YR.js?v=5981b4ff
2025-08-05T06:30:09.958Z vite:cache [memory] /node_modules/.vite/deps/chunk-LXGCQ6UQ.js?v=5981b4ff
2025-08-05T06:30:09.958Z vite:cache [memory] /node_modules/.vite/deps/chunk-5F26ILMS.js?v=5981b4ff
2025-08-05T06:30:09.958Z vite:cache [memory] /node_modules/.vite/deps/chunk-ROME4SDB.js?v=5981b4ff
2025-08-05T06:30:09.958Z vite:import-analysis 6.45ms [4 imports rewritten] node_modules/.vite/deps/chunk-WPFDAWNF.js?v=5981b4ff
2025-08-05T06:30:09.958Z vite:transform 9.15ms /node_modules/.vite/deps/chunk-WPFDAWNF.js?v=5981b4ff
2025-08-05T06:30:09.960Z vite:load 14.88ms [fs] /src/store/api/authApi.ts
2025-08-05T06:30:09.961Z vite:load 15.13ms [fs] /src/store/api/worldApi.ts
2025-08-05T06:30:09.961Z vite:load 15.29ms [fs] /src/store/api/gameInteractionApi.ts
2025-08-05T06:30:09.961Z vite:load 15.48ms [fs] /src/store/api/characterApi.ts
2025-08-05T06:30:09.961Z vite:load 15.69ms [fs] /src/store/api/aiApi.ts
2025-08-05T06:30:09.962Z vite:load 13.67ms [fs] /src/services/gameService.ts
2025-08-05T06:30:09.963Z vite:cache [memory] /src/store/api/apiSlice.ts
2025-08-05T06:30:09.963Z vite:import-analysis 0.28ms [1 imports rewritten] src/store/api/authApi.ts
2025-08-05T06:30:09.963Z vite:transform 2.31ms /src/store/api/authApi.ts
2025-08-05T06:30:09.963Z vite:cache [memory] /src/store/api/apiSlice.ts
2025-08-05T06:30:09.963Z vite:import-analysis 0.36ms [1 imports rewritten] src/store/api/gameInteractionApi.ts
2025-08-05T06:30:09.963Z vite:import-analysis 0.37ms [1 imports rewritten] src/store/api/worldApi.ts
2025-08-05T06:30:09.963Z vite:import-analysis 0.38ms [1 imports rewritten] src/store/api/aiApi.ts
2025-08-05T06:30:09.963Z vite:transform 2.52ms /src/store/api/gameInteractionApi.ts
2025-08-05T06:30:09.963Z vite:transform 2.70ms /src/store/api/worldApi.ts
2025-08-05T06:30:09.964Z vite:transform 2.36ms /src/store/api/aiApi.ts
2025-08-05T06:30:09.964Z vite:cache [memory] /src/store/api/apiSlice.ts
2025-08-05T06:30:09.964Z vite:import-analysis 0.17ms [1 imports rewritten] src/store/api/characterApi.ts
2025-08-05T06:30:09.964Z vite:transform 2.89ms /src/store/api/characterApi.ts
2025-08-05T06:30:09.964Z vite:cache [memory] /src/store/index.ts
2025-08-05T06:30:09.965Z vite:cache [memory] /src/store/slices/gameSlice.ts
2025-08-05T06:30:09.965Z vite:import-analysis 0.33ms [2 imports rewritten] src/services/gameService.ts
2025-08-05T06:30:09.965Z vite:transform 2.49ms /src/services/gameService.ts
